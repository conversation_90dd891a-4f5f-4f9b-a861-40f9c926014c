<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zod4 as zod } from 'sveltekit-superforms/adapters';
	import { createWbsMappingSchema } from '$lib/schemas/tender';
	import type { WbsItemTree } from '$lib/schemas/wbs';
	import type { TenderLineItemRow } from '$lib/schemas/tender';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import * as Form from '$lib/components/ui/form/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import * as Command from '$lib/components/ui/command/index.js';
	import { Button, buttonVariants } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import { cn } from '$lib/utils';
	import CheckIcon from '@lucide/svelte/icons/check';
	import ChevronsUpDownIcon from '@lucide/svelte/icons/chevrons-up-down';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';
	import PlusIcon from 'phosphor-svelte/lib/Plus';

	let {
		open = $bindable(false),
		lineItem,
		wbsItemsTree,
		form: formData,
	}: {
		open?: boolean;
		lineItem: TenderLineItemRow;
		wbsItemsTree: WbsItemTree[];
		form: any;
	} = $props();

	const form = superForm(formData, {
		validators: zod(createWbsMappingSchema),
		resetForm: true,
		onUpdated: ({ form }) => {
			if (form.valid) {
				open = false;
			}
		},
	});

	const { form: formStore, enhance } = form;

	// Flatten WBS tree for easier selection
	function flattenWbsTree(items: WbsItemTree[], depth = 0): Array<WbsItemTree & { depth: number }> {
		const result: Array<WbsItemTree & { depth: number }> = [];

		for (const item of items) {
			result.push({ ...item, depth });
			if (item.children.length > 0) {
				result.push(...flattenWbsTree(item.children, depth + 1));
			}
		}

		return result;
	}

	const flatWbsItems = $derived(flattenWbsTree(wbsItemsTree));

	const flatWbsOptions = $derived(
		flatWbsItems.map((item) => ({
			value: item.wbs_library_item_id,
			label: `${item.code} - ${item.description}`,
			code: item.code,
			description: item.description,
			depth: item.depth,
			hasChildren: item.children.length > 0,
		})),
	);

	let wbsOpen = $state(false);

	const wbsTriggerId = useId();

	function closeAndFocusTrigger(triggerId: string, setOpen: (value: boolean) => void) {
		setOpen(false);
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}

	// Selected WBS item for display
	const selectedWbsItem = $derived(
		flatWbsItems.find((item) => item.wbs_library_item_id === $formStore.wbs_library_item_id),
	);

	function getIndentStyle(depth: number) {
		return `padding-left: ${depth * 1.5}rem`;
	}
</script>

<Dialog.Root bind:open>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>Map Line Item to WBS</Dialog.Title>
			<Dialog.Description>
				Map "{lineItem.description}" to Work Breakdown Structure items.
			</Dialog.Description>
		</Dialog.Header>

		<form method="POST" action="?/createWbsMapping" use:enhance>
			<input type="hidden" name="line_item_id" value={lineItem.tender_line_item_id} />

			<div class="space-y-6">
				<!-- Line Item Summary -->
				<div class="rounded-lg border bg-gray-50 p-4">
					<h4 class="mb-2 text-sm font-medium text-gray-900">Line Item Details</h4>
					<div class="grid grid-cols-2 gap-4 text-sm">
						<div>
							<span class="text-gray-500">Description:</span>
							<span class="ml-2 font-medium">{lineItem.description}</span>
						</div>
						<div>
							<span class="text-gray-500">Quantity:</span>
							<span class="ml-2">{lineItem.quantity || 'N/A'} {lineItem.unit || ''}</span>
						</div>
						<div>
							<span class="text-gray-500">Unit Rate:</span>
							<span class="ml-2">{lineItem.unit_rate || 'N/A'}</span>
						</div>
						<div>
							<span class="text-gray-500">Subtotal:</span>
							<span class="ml-2 font-medium">{lineItem.subtotal || 'N/A'}</span>
						</div>
					</div>
				</div>

				<!-- WBS Item Selection -->
				<Form.Field {form} name="wbs_library_item_id">
					<Popover.Root bind:open={wbsOpen}>
						<Form.Control id={wbsTriggerId}>
							{#snippet children({ props })}
								<Form.Label>WBS Item *</Form.Label>
								<Popover.Trigger
									class={cn(
										buttonVariants({ variant: 'outline' }),
										'w-full items-start justify-between text-left whitespace-normal',
										'h-auto min-h-[3rem] py-3',
										!$formStore.wbs_library_item_id && 'text-muted-foreground',
									)}
									role="combobox"
									aria-expanded={wbsOpen}
									{...props}
								>
									{#if selectedWbsItem}
										<span class="flex max-w-full min-w-0 flex-1 flex-col gap-1 text-left">
											<span class="text-foreground font-mono text-sm">{selectedWbsItem.code}</span>
											<span class="text-muted-foreground max-w-full text-sm break-words"
												>{selectedWbsItem.description}</span
											>
										</span>
									{:else}
										<span class="flex-1">Select WBS item</span>
									{/if}
									<ChevronsUpDownIcon class="mt-1 ml-2 h-4 w-4 shrink-0 opacity-50" />
								</Popover.Trigger>
								<input hidden name={props.name} value={$formStore.wbs_library_item_id ?? ''} />
							{/snippet}
						</Form.Control>
						<Popover.Content class="w-[--radix-popover-trigger-width] max-w-[calc(100vw-2rem)] p-0">
							<Command.Root>
								<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
								<Command.Empty>No WBS item found.</Command.Empty>
								<Command.Group class="max-h-60 overflow-y-auto">
									{#each flatWbsOptions as option (option.value)}
										<Command.Item
											class="w-full items-start gap-3"
											value={option.label}
											onSelect={() => {
												$formStore.wbs_library_item_id = option.value;
												closeAndFocusTrigger(wbsTriggerId, (value) => (wbsOpen = value));
											}}
										>
											<div
												style={getIndentStyle(option.depth)}
												class="flex min-w-0 flex-1 items-start gap-2 text-left"
											>
												{#if option.hasChildren}
													<span class="mt-0.5 text-gray-400">📁</span>
												{:else}
													<span class="mt-0.5 text-gray-400">📄</span>
												{/if}
												<div class="flex min-w-0 flex-1 flex-col gap-1">
													<span class="text-foreground font-mono text-sm">{option.code}</span>
													<span class="text-muted-foreground text-sm break-words"
														>{option.description}</span
													>
												</div>
											</div>
											<CheckIcon
												class={cn(
													'mt-1 ml-auto h-4 w-4 shrink-0',
													option.value !== $formStore.wbs_library_item_id && 'text-transparent',
												)}
											/>
										</Command.Item>
									{/each}
								</Command.Group>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Coverage Percentage -->
				<Form.Field {form} name="coverage_percentage">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Coverage Percentage (%)</Form.Label>
							<Input
								{...props}
								type="number"
								min="0.01"
								max="100"
								step="0.01"
								placeholder="100"
								bind:value={$formStore.coverage_percentage}
							/>
							<Form.Description>
								What percentage of the WBS item does this line item cover?
							</Form.Description>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Coverage Quantity -->
				<Form.Field {form} name="coverage_quantity">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Coverage Quantity (Optional)</Form.Label>
							<Input
								{...props}
								type="number"
								min="0"
								step="0.0001"
								placeholder="Enter quantity"
								bind:value={$formStore.coverage_quantity}
							/>
							<Form.Description>
								Specific quantity of the WBS item covered by this line item.
							</Form.Description>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Notes -->
				<Form.Field {form} name="notes">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Notes (Optional)</Form.Label>
							<Textarea
								{...props}
								placeholder="Additional notes about this mapping..."
								bind:value={$formStore.notes}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<Dialog.Footer class="mt-6">
				<Button type="button" variant="outline" onclick={() => (open = false)}>Cancel</Button>
				<Button type="submit">
					<PlusIcon class="mr-2 h-4 w-4" />
					Create Mapping
				</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
